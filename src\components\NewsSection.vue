<template>
  <section class="news-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">农业资讯</h2>
        <div class="news-tabs">
          <button 
            v-for="tab in newsTabs" 
            :key="tab.value"
            :class="['tab-btn', { active: activeTab === tab.value }]"
            @click="activeTab = tab.value"
          >
            {{ tab.label }}
          </button>
        </div>
      </div>

      <div class="news-content">
        <!-- 主要新闻 -->
        <div class="main-news">
          <div class="featured-news" v-if="featuredNews">
            <div class="news-image">
              <img :src="featuredNews.image" :alt="featuredNews.title" />
              <div class="news-category">{{ featuredNews.category }}</div>
            </div>
            <div class="news-info">
              <h3 class="news-title">{{ featuredNews.title }}</h3>
              <p class="news-summary">{{ featuredNews.summary }}</p>
              <div class="news-meta">
                <span class="news-date">{{ featuredNews.date }}</span>
                <span class="news-source">{{ featuredNews.source }}</span>
                <span class="news-views">{{ featuredNews.views }} 阅读</span>
              </div>
            </div>
          </div>

          <div class="news-list">
            <div 
              v-for="(news, index) in currentNewsList" 
              :key="index"
              class="news-item"
              @click="handleNewsClick(news)"
            >
              <div class="news-thumbnail">
                <img :src="news.image" :alt="news.title" />
              </div>
              <div class="news-content-text">
                <h4 class="news-item-title">{{ news.title }}</h4>
                <p class="news-excerpt">{{ news.excerpt }}</p>
                <div class="news-item-meta">
                  <span class="news-tag">{{ news.tag }}</span>
                  <span class="news-time">{{ news.time }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="news-sidebar">
          <!-- 热门话题 -->
          <div class="sidebar-card">
            <h3 class="sidebar-title">热门话题</h3>
            <div class="hot-topics">
              <div 
                v-for="(topic, index) in hotTopics" 
                :key="index"
                class="topic-item"
                @click="handleTopicClick(topic)"
              >
                <span class="topic-rank">{{ index + 1 }}</span>
                <span class="topic-name">{{ topic.name }}</span>
                <span class="topic-count">{{ topic.count }}</span>
              </div>
            </div>
          </div>

          <!-- 政策速递 -->
          <div class="sidebar-card">
            <h3 class="sidebar-title">政策速递</h3>
            <div class="policy-list">
              <div 
                v-for="(policy, index) in policyNews" 
                :key="index"
                class="policy-item"
                @click="handlePolicyClick(policy)"
              >
                <div class="policy-title">{{ policy.title }}</div>
                <div class="policy-date">{{ policy.date }}</div>
              </div>
            </div>
          </div>

          <!-- 数据快报 -->
          <div class="sidebar-card">
            <h3 class="sidebar-title">数据快报</h3>
            <div class="data-reports">
              <div 
                v-for="(report, index) in dataReports" 
                :key="index"
                class="report-item"
              >
                <div class="report-icon">
                  <component :is="report.icon" />
                </div>
                <div class="report-content">
                  <div class="report-title">{{ report.title }}</div>
                  <div class="report-value">{{ report.value }}</div>
                  <div class="report-change" :class="report.trend">
                    {{ report.change }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'

// 图标组件
const TrendingUpIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
      <polyline points="17 6 23 6 23 12"></polyline>
    </svg>
  `
}

const BarChartIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <line x1="12" y1="20" x2="12" y2="10"></line>
      <line x1="18" y1="20" x2="18" y2="4"></line>
      <line x1="6" y1="20" x2="6" y2="16"></line>
    </svg>
  `
}

const DollarSignIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <line x1="12" y1="1" x2="12" y2="23"></line>
      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
    </svg>
  `
}

// 新闻标签页
const newsTabs = ref([
  { label: '全部', value: 'all' },
  { label: '市场动态', value: 'market' },
  { label: '技术创新', value: 'tech' },
  { label: '政策法规', value: 'policy' },
  { label: '国际资讯', value: 'international' }
])

const activeTab = ref('all')

// 特色新闻
const featuredNews = ref({
  title: '2024年全球小麦产量预计增长3.2%，供应链稳定性持续改善',
  summary: '根据最新的农业数据分析，今年全球小麦产量有望达到新高，主要得益于气候条件改善和农业技术进步。专家预测这将对全球粮食安全产生积极影响...',
  image: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=600&h=300&fit=crop',
  category: '市场分析',
  date: '2024-01-15',
  source: '农业部数据中心',
  views: '12.5k'
})

// 新闻列表数据
const newsData = ref({
  all: [
    {
      title: '智慧农业技术助力春耕生产效率提升40%',
      excerpt: '物联网、大数据等技术在农业生产中的应用越来越广泛...',
      image: 'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?w=150&h=100&fit=crop',
      tag: '技术创新',
      time: '2小时前'
    },
    {
      title: '农产品价格指数连续三月上涨，专家解读背后原因',
      excerpt: '受多重因素影响，主要农产品价格呈现稳中有升态势...',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=150&h=100&fit=crop',
      tag: '市场动态',
      time: '4小时前'
    },
    {
      title: '新版农业补贴政策正式发布，惠及千万农户',
      excerpt: '政策重点支持绿色农业发展和农业现代化建设...',
      image: 'https://images.unsplash.com/photo-1500937386664-56d1dfef3854?w=150&h=100&fit=crop',
      tag: '政策法规',
      time: '6小时前'
    },
    {
      title: '国际农产品贸易新格局：机遇与挑战并存',
      excerpt: '全球农产品贸易格局正在发生深刻变化...',
      image: 'https://images.unsplash.com/photo-1464226184884-fa280b87c399?w=150&h=100&fit=crop',
      tag: '国际资讯',
      time: '8小时前'
    }
  ],
  market: [
    {
      title: '农产品价格指数连续三月上涨，专家解读背后原因',
      excerpt: '受多重因素影响，主要农产品价格呈现稳中有升态势...',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=150&h=100&fit=crop',
      tag: '市场动态',
      time: '4小时前'
    }
  ],
  tech: [
    {
      title: '智慧农业技术助力春耕生产效率提升40%',
      excerpt: '物联网、大数据等技术在农业生产中的应用越来越广泛...',
      image: 'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?w=150&h=100&fit=crop',
      tag: '技术创新',
      time: '2小时前'
    }
  ],
  policy: [
    {
      title: '新版农业补贴政策正式发布，惠及千万农户',
      excerpt: '政策重点支持绿色农业发展和农业现代化建设...',
      image: 'https://images.unsplash.com/photo-1500937386664-56d1dfef3854?w=150&h=100&fit=crop',
      tag: '政策法规',
      time: '6小时前'
    }
  ],
  international: [
    {
      title: '国际农产品贸易新格局：机遇与挑战并存',
      excerpt: '全球农产品贸易格局正在发生深刻变化...',
      image: 'https://images.unsplash.com/photo-1464226184884-fa280b87c399?w=150&h=100&fit=crop',
      tag: '国际资讯',
      time: '8小时前'
    }
  ]
})

// 当前新闻列表
const currentNewsList = computed(() => {
  return newsData.value[activeTab.value] || newsData.value.all
})

// 热门话题
const hotTopics = ref([
  { name: '智慧农业', count: '2.3万' },
  { name: '粮食安全', count: '1.8万' },
  { name: '农业补贴', count: '1.5万' },
  { name: '绿色发展', count: '1.2万' },
  { name: '乡村振兴', count: '1.1万' }
])

// 政策新闻
const policyNews = ref([
  { title: '农业农村部发布春季田管技术指导意见', date: '01-15' },
  { title: '新一轮农业保险补贴政策出台', date: '01-12' },
  { title: '数字农业发展规划(2024-2026)发布', date: '01-10' }
])

// 数据快报
const dataReports = ref([
  {
    icon: TrendingUpIcon,
    title: '小麦价格',
    value: '¥2,845/吨',
    change: '+2.3%',
    trend: 'positive'
  },
  {
    icon: BarChartIcon,
    title: '库存水平',
    value: '68%',
    change: '-1.2%',
    trend: 'negative'
  },
  {
    icon: DollarSignIcon,
    title: '交易额',
    value: '¥12.5亿',
    change: '+5.8%',
    trend: 'positive'
  }
])

// 事件处理
const handleNewsClick = (news) => {
  console.log('点击新闻:', news.title)
}

const handleTopicClick = (topic) => {
  console.log('点击话题:', topic.name)
}

const handlePolicyClick = (policy) => {
  console.log('点击政策:', policy.title)
}
</script>

<style scoped>
.news-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 2.5rem;
  color: var(--primary-color);
  font-weight: 700;
}

.news-tabs {
  display: flex;
  gap: 10px;
}

.tab-btn {
  padding: 8px 20px;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.news-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
}

.featured-news {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.news-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-category {
  position: absolute;
  top: 15px;
  left: 15px;
  background: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.news-info {
  padding: 25px;
}

.news-title {
  font-size: 1.4rem;
  color: var(--text-primary);
  margin-bottom: 15px;
  font-weight: 600;
  line-height: 1.4;
}

.news-summary {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.news-meta {
  display: flex;
  gap: 20px;
  font-size: 0.85rem;
  color: var(--text-light);
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  gap: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.news-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.news-thumbnail {
  width: 120px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content-text {
  flex: 1;
}

.news-item-title {
  font-size: 1rem;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.4;
}

.news-excerpt {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 10px;
}

.news-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-tag {
  background: #e3f2fd;
  color: var(--primary-color);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.news-time {
  font-size: 0.8rem;
  color: var(--text-light);
}

.news-sidebar {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.sidebar-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.sidebar-title {
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 20px;
  font-weight: 600;
}

.hot-topics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.topic-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-item:hover {
  background: #f8f9fa;
  border-radius: 6px;
  padding-left: 8px;
}

.topic-rank {
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.topic-name {
  flex: 1;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.topic-count {
  font-size: 0.8rem;
  color: var(--text-light);
}

.policy-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.policy-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.policy-item:hover {
  background: #f8f9fa;
  border-radius: 6px;
  padding-left: 8px;
}

.policy-item:last-child {
  border-bottom: none;
}

.policy-title {
  font-size: 0.9rem;
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1.4;
}

.policy-date {
  font-size: 0.8rem;
  color: var(--text-light);
}

.data-reports {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.report-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 10px;
}

.report-icon {
  width: 36px;
  height: 36px;
  background: var(--primary-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.report-icon svg {
  width: 18px;
  height: 18px;
}

.report-content {
  flex: 1;
}

.report-title {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.report-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.report-change {
  font-size: 0.8rem;
  font-weight: 500;
}

.report-change.positive {
  color: #10b981;
}

.report-change.negative {
  color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .news-tabs {
    flex-wrap: wrap;
  }
  
  .news-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .news-item {
    flex-direction: column;
  }
  
  .news-thumbnail {
    width: 100%;
    height: 150px;
  }
}
</style>
