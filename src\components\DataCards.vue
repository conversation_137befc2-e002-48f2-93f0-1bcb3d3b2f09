<template>
  <section class="data-cards-section">
    <div class="cards-container">
      <div class="cards-grid">
        <div 
          v-for="(card, index) in dataCards" 
          :key="index"
          class="data-card"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="card-icon">
            <component :is="card.icon" />
          </div>
          <div class="card-content">
            <div class="card-number">{{ card.number }}</div>
            <div class="card-label">{{ card.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

// 图标组件
const DatabaseIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
      <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
      <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
    </svg>
  `
}

const TrendingUpIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
      <polyline points="17 6 23 6 23 12"></polyline>
    </svg>
  `
}

const FileTextIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
      <polyline points="14 2 14 8 20 8"></polyline>
      <line x1="16" y1="13" x2="8" y2="13"></line>
      <line x1="16" y1="17" x2="8" y2="17"></line>
      <polyline points="10 9 9 9 8 9"></polyline>
    </svg>
  `
}

const UsersIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
      <circle cx="9" cy="7" r="4"></circle>
      <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
    </svg>
  `
}

const GlobeIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="2" y1="12" x2="22" y2="12"></line>
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
    </svg>
  `
}

const BarChartIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <line x1="12" y1="20" x2="12" y2="10"></line>
      <line x1="18" y1="20" x2="18" y2="4"></line>
      <line x1="6" y1="20" x2="6" y2="16"></line>
    </svg>
  `
}

const ShoppingCartIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="9" cy="21" r="1"></circle>
      <circle cx="20" cy="21" r="1"></circle>
      <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
    </svg>
  `
}

const AwardIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="8" r="7"></circle>
      <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>
    </svg>
  `
}

// 静态数据卡片
const dataCards = ref([
  {
    icon: DatabaseIcon,
    number: '18292312',
    label: '数据总量'
  },
  {
    icon: TrendingUpIcon,
    number: '21',
    label: '实时监测'
  },
  {
    icon: FileTextIcon,
    number: '2265',
    label: '研究报告'
  },
  {
    icon: UsersIcon,
    number: '192',
    label: '专家团队'
  },
  {
    icon: GlobeIcon,
    number: '76202',
    label: '全球覆盖'
  },
  {
    icon: BarChartIcon,
    number: '404808',
    label: '数据分析'
  },
  {
    icon: ShoppingCartIcon,
    number: '13465',
    label: '市场数据'
  },
  {
    icon: AwardIcon,
    number: '234895',
    label: '服务用户'
  }
])
</script>

<style scoped>
.data-cards-section {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 30px 0;
  z-index: 10;
  margin-top: auto; /* 推到底部 */
}

.cards-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 20px;
  justify-items: center;
}

.data-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 120px;
  animation: slideUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.card-icon {
  width: 40px;
  height: 40px;
  margin: 0 auto 15px;
  color: #2c5aa0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon svg {
  width: 100%;
  height: 100%;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.card-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c5aa0;
  line-height: 1.2;
}

.card-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
  }
  
  .data-card {
    padding: 15px 10px;
    min-width: 90px;
  }
  
  .card-icon {
    width: 30px;
    height: 30px;
    margin-bottom: 10px;
  }
  
  .card-number {
    font-size: 1.2rem;
  }
  
  .card-label {
    font-size: 0.8rem;
  }
  
  .data-cards-section {
    padding: 20px 0;
  }
  
  .cards-container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .cards-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
  }
  
  .data-card {
    padding: 12px 8px;
  }
  
  .card-number {
    font-size: 1rem;
  }
  
  .card-label {
    font-size: 0.7rem;
  }
}
</style>
