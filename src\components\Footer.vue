<template>
  <footer class="footer">
    <div class="footer-main">
      <div class="container">
        <div class="footer-content">
          <!-- 公司信息 -->
          <div class="footer-section">
            <div class="footer-logo">
              <h3>HBN环球农业大数据平台</h3>
              <p class="footer-desc">
                致力于为全球农业提供专业的数据服务和智能分析，
                推动农业现代化发展，保障全球粮食安全。
              </p>
            </div>
            <div class="contact-info">
              <div class="contact-item">
                <span class="contact-icon">📞</span>
                <span>400-123-4567</span>
              </div>
              <div class="contact-item">
                <span class="contact-icon">📧</span>
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <span class="contact-icon">📍</span>
                <span>北京市朝阳区农业科技园区</span>
              </div>
            </div>
          </div>

          <!-- 产品服务 -->
          <div class="footer-section">
            <h4 class="footer-title">产品服务</h4>
            <ul class="footer-links">
              <li><a href="#" @click="handleLinkClick('data-analysis')">数据分析</a></li>
              <li><a href="#" @click="handleLinkClick('market-info')">市场行情</a></li>
              <li><a href="#" @click="handleLinkClick('weather')">气象服务</a></li>
              <li><a href="#" @click="handleLinkClick('policy')">政策法规</a></li>
              <li><a href="#" @click="handleLinkClick('supply-chain')">供应链管理</a></li>
              <li><a href="#" @click="handleLinkClick('research')">科研服务</a></li>
            </ul>
          </div>

          <!-- 解决方案 -->
          <div class="footer-section">
            <h4 class="footer-title">解决方案</h4>
            <ul class="footer-links">
              <li><a href="#" @click="handleLinkClick('smart-farming')">智慧农业</a></li>
              <li><a href="#" @click="handleLinkClick('precision-agriculture')">精准农业</a></li>
              <li><a href="#" @click="handleLinkClick('crop-monitoring')">作物监测</a></li>
              <li><a href="#" @click="handleLinkClick('risk-management')">风险管理</a></li>
              <li><a href="#" @click="handleLinkClick('yield-prediction')">产量预测</a></li>
              <li><a href="#" @click="handleLinkClick('market-analysis')">市场分析</a></li>
            </ul>
          </div>

          <!-- 支持中心 -->
          <div class="footer-section">
            <h4 class="footer-title">支持中心</h4>
            <ul class="footer-links">
              <li><a href="#" @click="handleLinkClick('help-center')">帮助中心</a></li>
              <li><a href="#" @click="handleLinkClick('api-docs')">API文档</a></li>
              <li><a href="#" @click="handleLinkClick('tutorials')">使用教程</a></li>
              <li><a href="#" @click="handleLinkClick('faq')">常见问题</a></li>
              <li><a href="#" @click="handleLinkClick('contact')">联系我们</a></li>
              <li><a href="#" @click="handleLinkClick('feedback')">意见反馈</a></li>
            </ul>
          </div>

          <!-- 关于我们 -->
          <div class="footer-section">
            <h4 class="footer-title">关于我们</h4>
            <ul class="footer-links">
              <li><a href="#" @click="handleLinkClick('company')">公司介绍</a></li>
              <li><a href="#" @click="handleLinkClick('team')">团队介绍</a></li>
              <li><a href="#" @click="handleLinkClick('news')">新闻动态</a></li>
              <li><a href="#" @click="handleLinkClick('careers')">招聘信息</a></li>
              <li><a href="#" @click="handleLinkClick('partners')">合作伙伴</a></li>
              <li><a href="#" @click="handleLinkClick('privacy')">隐私政策</a></li>
            </ul>
          </div>
        </div>

        <!-- 友情链接 -->
        <div class="partner-links">
          <h4 class="partner-title">合作伙伴</h4>
          <div class="partner-list">
            <a 
              v-for="partner in partners" 
              :key="partner.name"
              :href="partner.url" 
              class="partner-link"
              target="_blank"
              rel="noopener noreferrer"
            >
              {{ partner.name }}
            </a>
          </div>
        </div>

        <!-- 社交媒体 -->
        <div class="social-section">
          <h4 class="social-title">关注我们</h4>
          <div class="social-links">
            <a 
              v-for="social in socialLinks" 
              :key="social.name"
              :href="social.url" 
              class="social-link"
              :title="social.name"
              target="_blank"
              rel="noopener noreferrer"
            >
              <component :is="social.icon" />
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="footer-bottom">
      <div class="container">
        <div class="copyright-content">
          <div class="copyright-text">
            <p>&copy; 2024 HBN环球农业大数据平台. 保留所有权利.</p>
            <p>京ICP备12345678号-1 | 京公网安备11010502012345号</p>
          </div>
          <div class="footer-nav">
            <a href="#" @click="handleLinkClick('terms')">服务条款</a>
            <a href="#" @click="handleLinkClick('privacy')">隐私政策</a>
            <a href="#" @click="handleLinkClick('sitemap')">网站地图</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref } from 'vue'

// 社交媒体图标组件
const WechatIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.91-4.624-6.364-4.624z"/>
    </svg>
  `
}

const WeiboIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M9.31 8.17c-2.73-.13-4.3.87-4.3 2.15 0 .*********** *********.*********** 0 .36-.28.65-.62.65-.69 0-1.24-.87-1.24-1.94 0-2.02 2.02-3.46 4.92-3.32v.58z"/>
    </svg>
  `
}

const LinkedinIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
    </svg>
  `
}

const TwitterIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
    </svg>
  `
}

// 合作伙伴数据
const partners = ref([
  { name: '农业农村部', url: '#' },
  { name: '中科院', url: '#' },
  { name: '清华大学', url: '#' },
  { name: '北京农业大学', url: '#' },
  { name: '国际粮农组织', url: '#' },
  { name: '世界银行', url: '#' },
  { name: '联合国粮食计划署', url: '#' },
  { name: '中国农科院', url: '#' }
])

// 社交媒体链接
const socialLinks = ref([
  { name: '微信', icon: WechatIcon, url: '#' },
  { name: '微博', icon: WeiboIcon, url: '#' },
  { name: 'LinkedIn', icon: LinkedinIcon, url: '#' },
  { name: 'Twitter', icon: TwitterIcon, url: '#' }
])

// 链接点击处理
const handleLinkClick = (type) => {
  console.log('点击链接:', type)
  // 这里可以添加路由跳转或其他逻辑
}

// 定义事件
const emit = defineEmits(['link-click'])
</script>

<style scoped>
.footer {
  background: #1a1a1a;
  color: #ffffff;
}

.footer-main {
  padding: 60px 0 40px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4.footer-title {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.footer-logo h3 {
  color: var(--primary-light);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.footer-desc {
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 0.9rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #cccccc;
  font-size: 0.9rem;
}

.contact-icon {
  font-size: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #cccccc;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-light);
}

.partner-links {
  margin-bottom: 30px;
  padding-top: 30px;
  border-top: 1px solid #333;
}

.partner-title {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.partner-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.partner-link {
  color: #cccccc;
  text-decoration: none;
  font-size: 0.9rem;
  padding: 8px 15px;
  border: 1px solid #333;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.partner-link:hover {
  color: var(--primary-light);
  border-color: var(--primary-light);
  background: rgba(74, 144, 226, 0.1);
}

.social-section {
  padding-top: 30px;
  border-top: 1px solid #333;
}

.social-title {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cccccc;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.social-link svg {
  width: 20px;
  height: 20px;
}

.footer-bottom {
  background: #111;
  padding: 20px 0;
  border-top: 1px solid #333;
}

.copyright-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright-text {
  color: #888;
  font-size: 0.85rem;
}

.copyright-text p {
  margin: 0;
  line-height: 1.5;
}

.footer-nav {
  display: flex;
  gap: 20px;
}

.footer-nav a {
  color: #888;
  text-decoration: none;
  font-size: 0.85rem;
  transition: color 0.3s ease;
}

.footer-nav a:hover {
  color: var(--primary-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .partner-list {
    gap: 10px;
  }
  
  .partner-link {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
  
  .copyright-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .footer-nav {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-main {
    padding: 40px 0 30px;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .footer-nav {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
