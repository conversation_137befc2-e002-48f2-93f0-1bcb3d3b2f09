<template>
  <section class="function-nav-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">核心功能</h2>
        <p class="section-subtitle">一站式农业数据服务平台</p>
      </div>
      
      <div class="function-grid">
        <div 
          v-for="(item, index) in functionItems" 
          :key="index"
          class="function-item"
          :style="{ animationDelay: `${index * 0.1}s` }"
          @click="handleFunctionClick(item)"
        >
          <div class="function-icon">
            <component :is="item.icon" />
          </div>
          <div class="function-content">
            <h3 class="function-title">{{ item.title }}</h3>
            <p class="function-desc">{{ item.description }}</p>
            <div class="function-stats">
              <span class="stat-item">{{ item.dataCount }}</span>
              <span class="stat-label">数据量</span>
            </div>
          </div>
          <div class="function-arrow">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M9 18l6-6-6-6"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

// 图标组件
const DataAnalysisIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M3 3v18h18"/>
      <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"/>
    </svg>
  `
}

const MarketIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M2 7l10 12L22 7"/>
      <path d="M12 2v3"/>
      <path d="M22 7H2l3-4h14l3 4z"/>
    </svg>
  `
}

const WeatherIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/>
    </svg>
  `
}

const PolicyIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
      <polyline points="14 2 14 8 20 8"/>
      <line x1="16" y1="13" x2="8" y2="13"/>
      <line x1="16" y1="17" x2="8" y2="17"/>
    </svg>
  `
}

const SupplyChainIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <rect x="1" y="3" width="15" height="13"/>
      <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"/>
      <circle cx="5.5" cy="18.5" r="2.5"/>
      <circle cx="18.5" cy="18.5" r="2.5"/>
    </svg>
  `
}

const ResearchIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M9 11a3 3 0 1 0 6 0a3 3 0 0 0-6 0"/>
      <path d="M17.5 17.5L22 22"/>
      <circle cx="12" cy="12" r="10"/>
    </svg>
  `
}

// 功能模块数据
const functionItems = ref([
  {
    icon: DataAnalysisIcon,
    title: '数据分析',
    description: '农业生产数据智能分析，提供决策支持',
    dataCount: '1.2万+',
    path: '/data-analysis'
  },
  {
    icon: MarketIcon,
    title: '市场行情',
    description: '实时农产品价格监测与趋势预测',
    dataCount: '8.5万+',
    path: '/market'
  },
  {
    icon: WeatherIcon,
    title: '气象服务',
    description: '精准气象数据与农业气候分析',
    dataCount: '365天',
    path: '/weather'
  },
  {
    icon: PolicyIcon,
    title: '政策法规',
    description: '最新农业政策解读与法规查询',
    dataCount: '2.3千+',
    path: '/policy'
  },
  {
    icon: SupplyChainIcon,
    title: '供应链',
    description: '农产品供应链全程追溯管理',
    dataCount: '5.6万+',
    path: '/supply-chain'
  },
  {
    icon: ResearchIcon,
    title: '科研服务',
    description: '农业科研数据支持与文献检索',
    dataCount: '1.8万+',
    path: '/research'
  }
])

// 功能点击处理
const handleFunctionClick = (item) => {
  console.log('点击功能:', item.title)
  // 这里可以添加路由跳转逻辑
  // router.push(item.path)
}

// 定义事件
const emit = defineEmits(['function-click'])
</script>

<style scoped>
.function-nav-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 700;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.function-item {
  background: white;
  border-radius: 20px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.function-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.function-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.function-icon svg {
  width: 30px;
  height: 30px;
}

.function-content {
  flex: 1;
}

.function-title {
  font-size: 1.3rem;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-weight: 600;
}

.function-desc {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 12px;
}

.function-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-item {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-light);
}

.function-arrow {
  width: 24px;
  height: 24px;
  color: var(--text-light);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.function-item:hover .function-arrow {
  color: var(--primary-color);
  transform: translateX(5px);
}

.function-arrow svg {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .function-nav-section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .function-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .function-item {
    padding: 20px;
    gap: 15px;
  }
  
  .function-icon {
    width: 50px;
    height: 50px;
  }
  
  .function-icon svg {
    width: 24px;
    height: 24px;
  }
  
  .function-title {
    font-size: 1.1rem;
  }
  
  .function-desc {
    font-size: 0.9rem;
  }
}
</style>
