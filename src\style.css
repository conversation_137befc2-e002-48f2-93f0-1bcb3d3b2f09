/* 农业大数据平台全局样式 */
:root {
  /* 主色调 */
  --primary-color: #2c5aa0;
  --primary-light: #4a90e2;
  --primary-dark: #1e3d6f;

  /* 辅助色 */
  --secondary-color: #87CEEB;
  --accent-color: #98FB98;
  --warning-color: #F0E68C;

  /* 中性色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-white: #ffffff;
  --background-light: #f8f9fa;
  --border-color: #e1e5e9;

  /* 字体 */
  font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB',
               'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* 字体渲染优化 */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  margin: 0;
  color: var(--text-primary);
  background-color: var(--background-white);
  min-width: 320px;
  overflow-x: hidden;
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-light);
}

/* 按钮基础样式 */
button {
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  transition: all 0.3s ease;
  outline: none;
}

button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 输入框样式 */
input {
  font-family: inherit;
  font-size: inherit;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px 12px;
  transition: border-color 0.3s ease;
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.3;
}

/* 工具类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

/* 响应式断点 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
}
