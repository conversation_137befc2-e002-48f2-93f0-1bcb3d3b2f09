<template>
  <div class="search-container">
    <div class="search-tabs">
      <button 
        v-for="tab in searchTabs" 
        :key="tab.value"
        :class="['tab-btn', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>
    
    <div class="search-box">
      <input 
        v-model="searchQuery"
        type="text" 
        :placeholder="currentPlaceholder"
        class="search-input"
        @keyup.enter="handleSearch"
      />
      <button class="search-btn" @click="handleSearch">
        <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 搜索标签页数据
const searchTabs = ref([
  { label: '全部', value: 'all' },
  { label: '数据', value: 'data' },
  { label: '文献', value: 'literature' },
  { label: '政策', value: 'policy' },
  { label: '新闻', value: 'news' }
])

// 当前激活的标签页
const activeTab = ref('all')

// 搜索查询
const searchQuery = ref('')

// 根据当前标签页显示不同的占位符
const currentPlaceholder = computed(() => {
  const placeholders = {
    all: '请输入关键词，点击搜索按钮进行搜索',
    data: '搜索农业数据...',
    literature: '搜索相关文献...',
    policy: '搜索政策法规...',
    news: '搜索新闻资讯...'
  }
  return placeholders[activeTab.value] || placeholders.all
})

// 搜索处理函数
const handleSearch = () => {
  if (!searchQuery.value.trim()) {
    return
  }
  
  console.log('搜索类型:', activeTab.value)
  console.log('搜索关键词:', searchQuery.value)
  
  // 这里后续可以调用API进行搜索
  // emit('search', { type: activeTab.value, query: searchQuery.value })
}

// 定义事件
const emit = defineEmits(['search'])
</script>

<style scoped>
.search-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 10001 !important;
  transform: translateZ(0);
}

.search-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
}

.tab-btn {
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  position: relative;
  z-index: 10003;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.tab-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  font-weight: 600;
}

.search-box {
  display: flex;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 25px;
  padding: 5px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10002;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 20px;
  font-size: 16px;
  background: transparent;
  color: var(--text-primary);
}

.search-input::placeholder {
  color: var(--text-light);
}

.search-btn {
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  border: none;
  border-radius: 20px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
}

.search-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-tabs {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .tab-btn {
    padding: 6px 15px;
    font-size: 12px;
  }
  
  .search-input {
    font-size: 14px;
    padding: 10px 15px;
  }
  
  .search-btn {
    padding: 10px 15px;
  }
}
</style>
