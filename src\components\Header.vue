<template>
  <div class="header">
    <div class="header-container">
      <div class="logo">惠农平台</div>
      <nav class="nav-menu">
        <ul class="nav-list">
          <li class="nav-item">
            <a href="#" class="nav-link active">首页</a>
          </li>
          <li class="nav-item dropdown" @mouseenter="showDropdown('dataCenter')" @mouseleave="hideDropdown">
            <a href="#" class="nav-link">数据中心</a>
            <span class="dropdown-arrow">▼</span>
            <div class="dropdown-menu" v-show="activeDropdown === 'dataCenter'">
              <a href="#" class="dropdown-item">区域数据</a>
              <a href="#" class="dropdown-item">云观数据</a>
              <a href="#" class="dropdown-item">全球数据</a>
            </div>
          </li>
          <li class="nav-item dropdown" @mouseenter="showDropdown('marketData')" @mouseleave="hideDropdown">
            <a href="#" class="nav-link">专题数据</a>
            <span class="dropdown-arrow">▼</span>
            <div class="dropdown-menu" v-show="activeDropdown === 'marketData'">
              <a href="#" class="dropdown-item">气象数据</a>
              <a href="#" class="dropdown-item">灾害数据</a>
              <a href="#" class="dropdown-item">农业机械</a>
              <a href="#" class="dropdown-item">农业保险</a>

            </div>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">行情中心</a>
          </li>
          <li class="nav-item dropdown" @mouseenter="showDropdown('monitoring')" @mouseleave="hideDropdown">
            <a href="#" class="nav-link">监测预警</a>
            <span class="dropdown-arrow">▼</span>
            <div class="dropdown-menu" v-show="activeDropdown === 'monitoring'">
              <a href="#" class="dropdown-item">价格监测</a>
              <a href="#" class="dropdown-item">价格预警</a>
            </div>
          </li>
        </ul>
      </nav>
      <button class="login-btn">登录/注册</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 当前激活的下拉菜单
const activeDropdown = ref(null)

// 显示下拉菜单
const showDropdown = (menuName) => {
  activeDropdown.value = menuName
}

// 隐藏下拉菜单
const hideDropdown = () => {
  activeDropdown.value = null
}
</script>

<style scoped>
.header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 60px !important;
  background: white !important;
  border-bottom: 1px solid #ccc;
  z-index: 99999 !important;
  display: flex !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
  width: 100%;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #2c5aa0;
}

.nav-menu {
  flex: 1;
  margin-left: 40px;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 60px;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 60px; /* 确保所有导航项高度一致 */
}

.nav-item.dropdown {
  position: relative;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 0;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  height: 100%; /* 让链接占满整个导航项高度 */
}

.nav-link:hover,
.nav-link.active {
  color: #2c5aa0;
  font-weight: 600;
}

.dropdown-arrow {
  margin-left: 4px;
  font-size: 10px;
  color: #666;
  transition: transform 0.3s ease;
  line-height: 1; /* 确保箭头不影响行高 */
}

.nav-item:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 10px 0;
  min-width: 160px;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.1);
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: block;
  padding: 8px 20px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  border-bottom: none;
}

.dropdown-item:hover {
  background: #f8f9fa;
  color: #2c5aa0;
  padding-left: 25px;
}

.login-btn {
  background: linear-gradient(45deg, #2c5aa0, #4a90e2);
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .header-container {
    padding: 0 15px;
  }

  .logo {
    font-size: 20px;
  }

  .login-btn {
    padding: 6px 15px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 10px;
  }

  .logo {
    font-size: 18px;
  }

  .login-btn {
    padding: 5px 12px;
    font-size: 11px;
  }
}
</style>
