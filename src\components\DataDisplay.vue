<template>
  <section class="data-display-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">实时数据监控</h2>
        <p class="section-subtitle">全球农业数据实时更新，助力精准决策</p>
      </div>

      <div class="data-content">
        <!-- 左侧数据图表 -->
        <div class="charts-area">
          <div class="chart-card main-chart">
            <div class="chart-header">
              <h3>全球小麦产量趋势</h3>
              <div class="chart-controls">
                <button 
                  v-for="period in timePeriods" 
                  :key="period.value"
                  :class="['period-btn', { active: activePeriod === period.value }]"
                  @click="activePeriod = period.value"
                >
                  {{ period.label }}
                </button>
              </div>
            </div>
            <div class="chart-content">
              <div class="mock-chart">
                <div class="chart-bars">
                  <div 
                    v-for="(bar, index) in chartData" 
                    :key="index"
                    class="chart-bar"
                    :style="{ height: `${bar.value}%`, animationDelay: `${index * 0.1}s` }"
                  >
                    <div class="bar-value">{{ bar.label }}</div>
                  </div>
                </div>
                <div class="chart-axis">
                  <span v-for="year in years" :key="year">{{ year }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="chart-row">
            <div class="chart-card">
              <h4>价格指数</h4>
              <div class="price-chart">
                <div class="price-line">
                  <div 
                    v-for="(point, index) in priceData" 
                    :key="index"
                    class="price-point"
                    :style="{ 
                      left: `${(index / (priceData.length - 1)) * 100}%`,
                      bottom: `${point.value}%`
                    }"
                  ></div>
                </div>
                <div class="price-value">
                  <span class="current-price">¥2,845</span>
                  <span class="price-change positive">+2.3%</span>
                </div>
              </div>
            </div>

            <div class="chart-card">
              <h4>库存水平</h4>
              <div class="inventory-chart">
                <div class="inventory-circle">
                  <div class="circle-progress" :style="{ '--progress': '68%' }">
                    <span class="progress-text">68%</span>
                  </div>
                </div>
                <div class="inventory-info">
                  <div class="info-item">
                    <span class="info-label">当前库存</span>
                    <span class="info-value">2.4万吨</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧实时数据 -->
        <div class="realtime-data">
          <div class="data-card">
            <h3>实时监测数据</h3>
            <div class="data-list">
              <div 
                v-for="(item, index) in realtimeData" 
                :key="index"
                class="data-item"
              >
                <div class="data-icon">
                  <component :is="item.icon" />
                </div>
                <div class="data-info">
                  <div class="data-name">{{ item.name }}</div>
                  <div class="data-value">{{ item.value }}</div>
                  <div class="data-trend" :class="item.trend">
                    {{ item.change }}
                  </div>
                </div>
                <div class="data-status" :class="item.status"></div>
              </div>
            </div>
          </div>

          <div class="data-card">
            <h3>区域分布</h3>
            <div class="region-list">
              <div 
                v-for="(region, index) in regionData" 
                :key="index"
                class="region-item"
              >
                <div class="region-name">{{ region.name }}</div>
                <div class="region-bar">
                  <div 
                    class="region-progress" 
                    :style="{ width: `${region.percentage}%` }"
                  ></div>
                </div>
                <div class="region-value">{{ region.value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

// 图标组件
const ThermometerIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z"/>
    </svg>
  `
}

const DropletIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/>
    </svg>
  `
}

const WindIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M17.7 7.7a2.5 2.5 0 1 1 1.8 4.3H2"/>
      <path d="M9.6 4.6A2 2 0 1 1 11 8H2"/>
      <path d="M12.6 19.4A2 2 0 1 0 14 16H2"/>
    </svg>
  `
}

const SunIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="5"/>
      <line x1="12" y1="1" x2="12" y2="3"/>
      <line x1="12" y1="21" x2="12" y2="23"/>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
      <line x1="1" y1="12" x2="3" y2="12"/>
      <line x1="21" y1="12" x2="23" y2="12"/>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
    </svg>
  `
}

// 时间周期选项
const timePeriods = ref([
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '3个月', value: '3m' },
  { label: '1年', value: '1y' }
])

const activePeriod = ref('30d')

// 图表数据
const chartData = ref([
  { label: '2020', value: 65 },
  { label: '2021', value: 78 },
  { label: '2022', value: 85 },
  { label: '2023', value: 92 },
  { label: '2024', value: 88 }
])

const years = ref(['2020', '2021', '2022', '2023', '2024'])

// 价格数据
const priceData = ref([
  { value: 45 },
  { value: 52 },
  { value: 48 },
  { value: 65 },
  { value: 72 },
  { value: 68 },
  { value: 75 }
])

// 实时数据
const realtimeData = ref([
  {
    icon: ThermometerIcon,
    name: '平均气温',
    value: '24.5°C',
    change: '+1.2°C',
    trend: 'positive',
    status: 'normal'
  },
  {
    icon: DropletIcon,
    name: '土壤湿度',
    value: '68%',
    change: '-3%',
    trend: 'negative',
    status: 'warning'
  },
  {
    icon: WindIcon,
    name: '风速',
    value: '12 km/h',
    change: '+0.5',
    trend: 'positive',
    status: 'normal'
  },
  {
    icon: SunIcon,
    name: '光照强度',
    value: '850 lux',
    change: '+25',
    trend: 'positive',
    status: 'good'
  }
])

// 区域数据
const regionData = ref([
  { name: '华北地区', percentage: 85, value: '2.1万吨' },
  { name: '华东地区', percentage: 72, value: '1.8万吨' },
  { name: '华南地区', percentage: 68, value: '1.6万吨' },
  { name: '西北地区', percentage: 45, value: '1.1万吨' },
  { name: '东北地区', percentage: 92, value: '2.3万吨' }
])
</script>

<style scoped>
.data-display-section {
  padding: 80px 0;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 700;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.data-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.charts-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.main-chart {
  flex: 1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.chart-header h3 {
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.period-btn {
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-btn.active,
.period-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.mock-chart {
  height: 200px;
  position: relative;
}

.chart-bars {
  display: flex;
  align-items: end;
  height: 180px;
  gap: 20px;
  padding: 0 20px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--primary-color), var(--primary-light));
  border-radius: 4px 4px 0 0;
  position: relative;
  animation: growUp 0.8s ease-out forwards;
  transform-origin: bottom;
  transform: scaleY(0);
}

@keyframes growUp {
  to {
    transform: scaleY(1);
  }
}

.bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.chart-axis {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px 0;
  font-size: 0.85rem;
  color: var(--text-light);
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-row .chart-card h4 {
  margin-bottom: 20px;
  color: var(--text-primary);
  font-size: 1rem;
}

.price-chart {
  position: relative;
  height: 100px;
}

.price-line {
  position: relative;
  height: 60px;
  border-bottom: 1px solid #f0f0f0;
}

.price-point {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  transform: translate(-50%, 50%);
}

.price-value {
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
}

.price-change {
  font-size: 0.9rem;
  font-weight: 600;
}

.price-change.positive {
  color: #10b981;
}

.price-change.negative {
  color: #ef4444;
}

.inventory-chart {
  display: flex;
  align-items: center;
  gap: 20px;
}

.inventory-circle {
  position: relative;
  width: 80px;
  height: 80px;
}

.circle-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(var(--primary-color) var(--progress), #f0f0f0 var(--progress));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-progress::before {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
}

.progress-text {
  position: relative;
  z-index: 1;
  font-weight: 700;
  color: var(--primary-color);
}

.inventory-info {
  flex: 1;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 0.85rem;
  color: var(--text-light);
}

.info-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.realtime-data {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.data-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.data-card h3 {
  margin-bottom: 20px;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 10px;
  background: #f8f9fa;
}

.data-icon {
  width: 36px;
  height: 36px;
  background: var(--primary-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.data-icon svg {
  width: 18px;
  height: 18px;
}

.data-info {
  flex: 1;
}

.data-name {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.data-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.data-trend {
  font-size: 0.8rem;
  font-weight: 500;
}

.data-trend.positive {
  color: #10b981;
}

.data-trend.negative {
  color: #ef4444;
}

.data-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.data-status.normal {
  background: #10b981;
}

.data-status.warning {
  background: #f59e0b;
}

.data-status.good {
  background: #3b82f6;
}

.region-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.region-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.region-name {
  width: 80px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.region-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.region-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: 4px;
  transition: width 1s ease-out;
}

.region-value {
  width: 60px;
  text-align: right;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .inventory-chart {
    flex-direction: column;
    text-align: center;
  }
}
</style>
