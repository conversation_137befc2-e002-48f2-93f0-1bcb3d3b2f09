<template>
  <div class="hero-section">
    <div class="hero-background">
      <!-- 背景装饰元素 -->
      <div class="bg-clouds">
        <div class="cloud cloud-1"></div>
        <div class="cloud cloud-2"></div>
        <div class="cloud cloud-3"></div>
      </div>
      
      <!-- 农业场景装饰 -->
      <div class="farm-elements">
        <div class="tractor"></div>
        <div class="windmill"></div>
        <div class="houses"></div>
        <div class="fields"></div>
      </div>
    </div>

    <div class="hero-content">
      <div class="hero-container">
        <!-- 主标题 -->
        <div class="title-section">
          <h1 class="main-title">惠农农业大数据平台</h1>
          <p class="subtitle">全球农业数据智能分析与决策支持平台</p>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
          <SearchBox @search="handleSearch" />
        </div>
      </div>
    </div>
    <div class="right-nav">
      dadaadada
    </div>
</template>

<script setup>
import SearchBox from './SearchBox.vue'

// 处理搜索事件
const handleSearch = (searchData) => {
  console.log('Hero section received search:', searchData)
  // 这里可以处理搜索逻辑或向父组件传递事件
}
</script>

<style scoped>
.hero-section {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #87CEEB 0%, #98FB98 50%, #F0E68C 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为顶部对齐 */
  padding-top: 60px; /* 为固定导航栏留出空间 */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bg-clouds {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1; /* 确保云朵在最底层 */
}

.cloud {
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  opacity: 0.7;
}

.cloud::before,
.cloud::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
}

.cloud-1 {
  width: 100px;
  height: 40px;
  top: 20%;
  left: 10%;
  animation: float 20s infinite linear;
}

.cloud-1::before {
  width: 50px;
  height: 50px;
  top: -25px;
  left: 10px;
}

.cloud-1::after {
  width: 60px;
  height: 40px;
  top: -15px;
  right: 10px;
}

.cloud-2 {
  width: 80px;
  height: 30px;
  top: 15%;
  right: 20%;
  animation: float 25s infinite linear reverse;
}

.cloud-2::before {
  width: 40px;
  height: 40px;
  top: -20px;
  left: 15px;
}

.cloud-2::after {
  width: 50px;
  height: 30px;
  top: -10px;
  right: 15px;
}

.cloud-3 {
  width: 120px;
  height: 50px;
  top: 25%;
  left: 60%;
  animation: float 30s infinite linear;
}

.cloud-3::before {
  width: 60px;
  height: 60px;
  top: -30px;
  left: 20px;
}

.cloud-3::after {
  width: 70px;
  height: 50px;
  top: -20px;
  right: 20px;
}

@keyframes float {
  from {
    transform: translateX(-100px);
  }
  to {
    transform: translateX(calc(100vw + 100px));
  }
}

.farm-elements {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200px;
  z-index: 2; /* 确保装饰元素在背景之上，但在搜索栏之下 */
}

.tractor {
  position: absolute;
  bottom: 50px;
  left: 5%;
  width: 60px;
  height: 40px;
  background: #ff6b35;
  border-radius: 5px;
}

.tractor::before {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 5px;
  width: 20px;
  height: 20px;
  background: #333;
  border-radius: 50%;
}

.tractor::after {
  content: '';
  position: absolute;
  bottom: -15px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: #333;
  border-radius: 50%;
}

.windmill {
  position: absolute;
  bottom: 80px;
  right: 10%;
  width: 8px;
  height: 80px;
  background: #8B4513;
}

.windmill::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: #fff;
  border-radius: 50%;
  animation: spin 3s linear infinite;
}

@keyframes spin {
  from {
    transform: translateX(-50%) rotate(0deg);
  }
  to {
    transform: translateX(-50%) rotate(360deg);
  }
}

.houses {
  position: absolute;
  bottom: 60px;
  right: 25%;
  width: 80px;
  height: 40px;
  background: #8B4513;
}

.houses::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  width: 80px;
  height: 20px;
  background: #ff4444;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.fields {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: linear-gradient(90deg, #90EE90 0%, #98FB98 50%, #90EE90 100%);
  border-radius: 50% 50% 0 0;
}

.hero-content {
  position: relative;
  z-index: 1000; /* 确保内容在装饰元素之上 */
  width: 100%;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为顶部对齐 */
  padding: 80px 0 40px 0; /* 适中的顶部padding */
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.right-nav{
  width: 100px;
  height: 100px;
  /* position: right; */
  background-color: #ff4444;
}
.title-section {
  margin-bottom: 0; /* 移除底部间距，由搜索区域的margin-top控制 */
}

.main-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 15px 0; /* 减少标题底部间距 */
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

.search-section {
  margin-top: 30px; /* 减少标题和搜索栏之间的距离 */
  position: relative;
  z-index: 10000 !important; /* 确保搜索栏在最上层 */
  transform: translateZ(0); /* 强制创建新的层叠上下文 */
  isolation: isolate; /* 创建新的层叠上下文 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .hero-container {
    padding: 0 15px;
  }
  
  .farm-elements {
    height: 150px;
  }
  
  .tractor {
    width: 40px;
    height: 25px;
  }
  
  .windmill {
    height: 60px;
  }
  
  .houses {
    width: 60px;
    height: 30px;
  }
}
</style>
