<script setup>
import Header from './components/Header.vue'
import HeroSection from './components/HeroSection.vue'
import DataCards from './components/DataCards.vue'
import FunctionNav from './components/FunctionNav.vue'
import DataDisplay from './components/DataDisplay.vue'
import NewsSection from './components/NewsSection.vue'
import Footer from './components/Footer.vue'
</script>

<template>
  <div class="app">
    <!-- 顶部导航栏 -->
    <Header />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- Hero区域包含标题、搜索和数据卡片 -->
      <div class="hero-wrapper">
        <HeroSection />
        <DataCards />
      </div>

      <!-- 功能导航区域 -->
      <FunctionNav />

      <!-- 数据展示区域 -->
      <DataDisplay />

      <!-- 新闻资讯区域 -->
      <NewsSection />
    </main>

    <!-- 底部信息 -->
    <Footer />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

#app {
  height: 100%;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  position: relative;
  padding-top: 60px; /* 为固定头部留出空间 */
}

.hero-wrapper {
  position: relative;
  height: calc(100vh - 60px); /* 减去头部高度 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
}
</style>
